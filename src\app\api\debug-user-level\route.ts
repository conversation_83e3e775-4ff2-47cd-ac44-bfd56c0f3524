// src/app/api/debug-user-level/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserLevelManager } from '@/lib/userLevelStorage'

interface DebugUserLevelRequest {
  userId: string
}

export async function POST(req: NextRequest) {
  try {
    const accessKey = req.headers.get('accesskey')

    if (!accessKey) {
      return NextResponse.json({ error: 'Missing required header: accessKey' }, { status: 400 })
    }

    if (accessKey !== process.env.ACCESS_KEY_FE) {
      return NextResponse.json({ error: 'Invalid access key' }, { status: 401 })
    }

    const body: DebugUserLevelRequest = await req.json()
    console.log('Debug User Level API - Incoming Request:', { accessKey, ...body })

    if (!body.userId) {
      return NextResponse.json({ error: 'Missing required field: userId' }, { status: 400 })
    }

    const userData = UserLevelManager.getUserData(body.userId)
    const allUserData = UserLevelManager.getAllUserData()

    const response = {
      requestedUser: userData,
      allUsers: allUserData,
      userExists: !!userData
    }

    console.log('Debug User Level API - Response:', response)
    return NextResponse.json(response)
  } catch (error) {
    console.error('Debug User Level API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
